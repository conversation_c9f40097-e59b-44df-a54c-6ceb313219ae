// Global variables
let currentPage = 1;
let currentData = null; // Holds the full API response (recommendations and summary)
let allApplications = []; // Holds processed application objects for cards
let filteredApplications = null; // Holds filtered application objects for cards
let currentFrequencyFilter = "";
let executionChart = null; // For Owner/Client Execution Analysis chart
let appExecutionChart = null; // For Application Execution Analysis chart
const itemsPerPage = 8;

// --- D3 Cluster Visualization Globals ---
window.clusterVizSvg = null;
window.clusterVizSvgGroup = null;
window.clusterVizZoom = null;
window.clusterVizSimulation = null;
window.clusterVizCurrentTransform = null;
window.isClusterFullscreen = false; // Track fullscreen state


// --- Helper Functions ---

/**
 * Debounce function to limit the rate at which a function can fire.
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * Format Select2 dropdown options.
 * Returns HTML string to be rendered.
 */
function formatOption(option) {
    if (!option.id) {
        return option.text; // Placeholder or group header
    }
    // Using template literal for clarity and adding the class
    return `<span class="select-option">${option.text}</span>`;
}

/**
 * Check if an element is within the browser viewport.
 */
function isElementInViewport(el) {
    if (!el) {
        return false;
    }
    const rect = el.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

/**
 * Show or hide a global loading indicator.
 */
function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    } else {
        // console.warn("Loading overlay element ('loadingOverlay') not found.");
    }
}

/**
 * Display an error message in a designated area or fallback to alert.
 */
function showError(message, containerId = 'applicationsContainer') {
    const container = document.getElementById(containerId);
    const errorDisplayId = 'globalErrorMessage';
    let errorDisplay = document.getElementById(errorDisplayId);

    if (container && containerId !== 'globalErrorContainer') {
         container.innerHTML = `
             <div class="error-message" style="padding: 20px; border: 1px solid red; color: red; margin: 10px;">
                 <p><strong>Error:</strong> ${message}</p>
             </div>
         `;
    }
    else {
        if (!errorDisplay) {
            const body = document.body;
            if (!body) return;
            errorDisplay = document.createElement('div');
            errorDisplay.id = errorDisplayId;
            // Basic styling for the global error message
            Object.assign(errorDisplay.style, {
                position: 'fixed', top: '10px', left: '50%', transform: 'translateX(-50%)',
                backgroundColor: 'rgba(255, 0, 0, 0.8)', color: 'white', padding: '10px 20px',
                borderRadius: '5px', zIndex: '9999', display: 'none'
            });
            body.appendChild(errorDisplay);
        }
        errorDisplay.innerHTML = `<strong>Error:</strong> ${message} <button onclick="this.parentElement.style.display='none'" style="margin-left: 15px; background: none; border: none; color: white; font-weight: bold; cursor: pointer;">X</button>`;
        errorDisplay.style.display = 'block';
        setTimeout(() => {
            if(errorDisplay) errorDisplay.style.display = 'none';
        }, 8000);
    }
    console.error('Error displayed:', message);
    showLoading(false);
}


// --- Initialization and Event Setup ---

/**
 * Main setup function called when the DOM is ready.
 */
document.addEventListener('DOMContentLoaded', () => {
    initializeFilters();
    setupFrequencyTags();
    setupClearFilters();
    setupApplyLLevelFilters();
    setupRecommendationTabs();
    setupPaginationListeners();
});

// --- Tab Management ---

/**
 * Sets up click handlers for the main navigation tabs.
 * Handles loading dynamic content for specific tabs.
 */
function setupRecommendationTabs() {
    const tabs = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');
    if (!tabs.length || !tabPanes.length) {
        console.warn("Tab buttons or panels not found. Unable to initialize tabs.");
        return;
    }

    tabs.forEach(tab => {
        const tabId = tab.getAttribute('data-tab');
        if (!tabId) {
            console.warn('Tab without data-tab attribute:', tab);
            return;
        }

        tab.addEventListener('click', async () => {
            console.log(`[Tab Click] Clicked on tab: ${tabId}`);
            try {
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                tabPanes.forEach(pane => pane.classList.remove('active'));

                const targetPane = document.getElementById(`${tabId}-tab`);
                if (!targetPane) {
                    console.warn('Target tab panel not found:', `${tabId}-tab`);
                    return;
                }

                targetPane.classList.add('active');
                console.log(`Tab panel activated: #${targetPane.id}`);

                let contentUrl = null;
                let postLoadAction = null;

                // Define URLs and post-load actions based on tab ID
                switch (tabId) {
                    case 'summary':
                        console.log(`Existing content activated for 'summary' tab.`);
                        postLoadAction = () => {
                             if (currentData && currentData.recommendations) {
                                 console.log("[Tab Content] Refreshing summary charts.");
                                 createExecutionChart(currentData.recommendations);
                                 createAppExecutionChart(currentData.recommendations);
                             }
                        };
                        break;
                    case 'org-funnel':
                        contentUrl = '/assets/analysis/org_recommendation_funnel.html';
                        postLoadAction = () => {
                            console.log("[Tab Content] Running postLoadAction for org-funnel.");
                            setupFunnelInteractivity();
                            updateOrganizationalRecommendations(currentData?.metadata?.org_recommendations);
                        };
                        break;
                    case 'product-rankings':
                        contentUrl = '/assets/analysis/product_rankings.html';
                        postLoadAction = () => {
                            console.log("[Tab Content] Running postLoadAction for product-rankings.");
                            updateProductRecommendations(currentData?.metadata?.product_recommendations);
                        };
                        break;
                    case 'enhanced-correlation':
                        contentUrl = '/assets/analysis/enhanced_product_correlation.html';
                        postLoadAction = () => {
                            console.log("[Tab Content] Running postLoadAction for enhanced-correlation.");
                            const ownerSearchTerm = document.getElementById('appOwnerSearch')?.value.trim() || "";
                            if (typeof window.loadClusterVisualization === 'function') {
                                 window.loadClusterVisualization(ownerSearchTerm);
                                 console.log('Enhanced correlation visualization container loaded, triggering data load.');
                            } else {
                                 console.error("window.loadClusterVisualization is not defined.");
                                 showError("Error loading visualization: function not found.", targetPane.id);
                            }
                        };
                        break;
                    default:
                        console.warn('Unknown tab ID or tab with static content:', tabId);
                        return;
                }

                // --- CORREÇÃO NA LÓGICA DE CARREGAMENTO ---
                let needsLoad = false;
                const isLoading = targetPane.querySelector('.loading-spinner') !== null; // Check if already loading

                if (contentUrl) { // Only check for dynamic tabs
                    // Check if the specific main content element for the tab is missing
                    if (tabId === 'org-funnel') {
                        needsLoad = targetPane.querySelector('.funnel-container') === null;
                    } else if (tabId === 'product-rankings') {
                        // Check for either container, as the function uses both
                        needsLoad = targetPane.querySelector('.product-rankings') === null || targetPane.querySelector('.product-recommendations') === null;
                    } else if (tabId === 'enhanced-correlation') {
                        needsLoad = targetPane.querySelector('#clusterVisualizationContainer') === null;
                    }
                    // Always reload if currently showing the loading spinner
                    needsLoad = needsLoad || isLoading;
                    console.log(`[Tab Load Check] Tab: ${tabId}, NeedsLoad: ${needsLoad} (Structure Missing: ${needsLoad && !isLoading}, IsLoading: ${isLoading})`);
                }
                // --- FIM DA CORREÇÃO ---

                if (contentUrl && needsLoad) {
                    console.log(`Fetching content for '${tabId}' from: ${contentUrl}`);
                    targetPane.innerHTML = `<div class="loading-spinner" style="margin: 50px auto;"></div> Loading...`;

                    try {
                        const response = await fetch(contentUrl);
                        const responseText = await response.text(); // Get text regardless of status
                        if (!response.ok) {
                            // Log the received text even on error for debugging
                            console.error(`[Tab Content] Fetch failed for ${tabId}. Status: ${response.status}. Response: ${responseText}`);
                            throw new Error(`HTTP Error! Status: ${response.status} fetching ${contentUrl}`);
                        }
                        targetPane.innerHTML = responseText; // Load the fetched HTML
                        console.log(`Content successfully loaded in #${targetPane.id}`);

                        setTimeout(() => {
                            if (postLoadAction) {
                                try {
                                    console.log(`[Tab Content] Running postLoadAction for ${tabId} after fetch.`);
                                    postLoadAction();
                                } catch (e) {
                                    console.error(`Error executing post-load action for tab ${tabId}:`, e);
                                    showError(`Failed to initialize content for ${tabId}: ${e.message}`, targetPane.id);
                                }
                            }
                        }, 50); // Small delay might help rendering

                    } catch (error) {
                        console.error(`Error loading content for tab '${tabId}':`, error);
                        // Display error message, keeping the targetPane ID for context
                        targetPane.innerHTML = `<div class="error-message" style="padding: 20px;">Failed to load content for ${tabId}: ${error.message}</div>`;
                    }
                } else if (contentUrl && !needsLoad) {
                    // Content structure already exists, just run post-load action (e.g., update data)
                    console.log(`Content for #${targetPane.id} already present. Executing post-load action.`);
                    setTimeout(() => {
                        if (postLoadAction) {
                            try {
                                console.log(`[Tab Content] Running postLoadAction for existing content in ${tabId}.`);
                                postLoadAction();
                            } catch(e) {
                                console.error(`Error executing post-load action for existing content in tab ${tabId}:`, e);
                                // Optionally show an update error, but don't clear existing content
                                // showError(`Failed to update content for ${tabId}: ${e.message}`, 'globalErrorContainer');
                            }
                        }
                    }, 50);
                } else if (postLoadAction) {
                    // Static tab (like Summary) - always run postLoadAction if defined
                    setTimeout(() => {
                        try {
                            console.log(`[Tab Content] Running postLoadAction for static tab ${tabId}.`);
                            postLoadAction();
                        } catch(e) {
                            console.error(`Error executing post-load action for static tab ${tabId}:`, e);
                        }
                    }, 50);
                }

            } catch (error) {
                console.error('Error handling tab click:', error);
                const targetPane = document.getElementById(`${tabId}-tab`);
                if (targetPane) {
                    targetPane.innerHTML = `<div class="error-message" style="padding: 20px;">An error occurred when switching tabs: ${error.message}</div>`;
                } else {
                    showError(`An error occurred when switching tabs: ${error.message}`, 'globalErrorContainer');
                }
            }
        });
    });

    // Activate the first tab ('summary') by default
    const firstTab = document.querySelector('.tab-button[data-tab="summary"]');
    if (firstTab) {
        setTimeout(() => { firstTab.click(); }, 0);
    } else if (tabs[0]) {
         setTimeout(() => { tabs[0].click(); }, 0);
    } else {
        console.warn("No tab found to activate initially.");
    }
}

// --- Filter Setup ---

/**
 * Fetches filter options and initializes filter dropdowns and search inputs.
 * Also triggers the initial data load.
 */
async function initializeFilters() {
    try {
        const response = await fetch('/api/filters');
        if (!response.ok) {
            throw new Error(`HTTP Error: ${response.status} when loading filter options`);
        }
        const filterOptions = await response.json();

        // -- App Name Filter --
        const appFilter = document.getElementById('appFilter');
        if (appFilter && filterOptions.app_names && Array.isArray(filterOptions.app_names)) {
            if (typeof $ === 'undefined' || !$.fn.select2) {
                 const placeholder = document.createElement('option');
                 placeholder.value = ""; placeholder.textContent = "Select Application";
                 placeholder.disabled = true; placeholder.selected = true;
                 appFilter.appendChild(placeholder);
            }
            filterOptions.app_names.sort((a, b) => a.localeCompare(b));
            filterOptions.app_names.forEach(app => {
                const option = document.createElement('option');
                option.value = app; option.textContent = app;
                appFilter.appendChild(option);
            });
            if (typeof $ !== 'undefined' && $.fn.select2) {
                 $('#appFilter').select2({
                     width: '100%', placeholder: 'Type to search application', allowClear: true, minimumInputLength: 0,
                     language: { noResults: () => "No application found", searching: () => "Searching...", inputTooShort: () => 'Type to search' },
                     templateResult: formatOption,
                     dropdownCssClass: 'enhanced-dropdown', selectionCssClass: 'enhanced-selection',
                     escapeMarkup: function (markup) { return markup; } // FIX ADDED
                 }).on('change', () => loadFilteredData());
            } else {
                 console.warn("jQuery/Select2 not found for Application Filter. Using basic select.");
                 appFilter.addEventListener('change', () => loadFilteredData());
            }
        } else {
             console.warn("Application filter element or options not found/invalid.");
        }

        // -- Product Filter --
        const productFilter = document.getElementById('productFilter');
        if (productFilter && filterOptions.products && Array.isArray(filterOptions.products)) {
            // Clear existing options except placeholder
            productFilter.innerHTML = '';
            if (productFilter.multiple || productFilter.dataset.placeholder) {
                const placeholder = document.createElement('option');
                placeholder.value = ""; placeholder.textContent = "Select Product";
                placeholder.disabled = true; placeholder.selected = true;
                productFilter.appendChild(placeholder);
            }
            filterOptions.products.sort((a, b) => a.localeCompare(b));
            filterOptions.products.forEach(product => {
                const option = document.createElement('option');
                option.value = product; option.textContent = product;
                productFilter.appendChild(option);
            });
            if (typeof $ !== 'undefined' && $.fn.select2) {
                 $('#productFilter').select2({
                     width: '100%', placeholder: 'Type to search product', allowClear: true, minimumInputLength: 0,
                     language: { noResults: () => "No product found", searching: () => "Searching...", inputTooShort: () => 'Type to search' },
                     templateResult: formatOption,
                     dropdownCssClass: 'enhanced-dropdown', selectionCssClass: 'enhanced-selection',
                     escapeMarkup: function (markup) { return markup; }
                 }).on('change', () => loadFilteredData());
            } else {
                 console.warn("jQuery/Select2 not found for Product Filter. Using basic select.");
                 productFilter.addEventListener('change', () => loadFilteredData());
            }
        } else {
             console.warn("Product filter element or options not found/invalid.");
        }

        // -- Urgency Filter --
        const urgencyFilter = document.getElementById('urgencyFilter');
        if (urgencyFilter) {
             if (typeof $ !== 'undefined' && $.fn.select2) {
                 $('#urgencyFilter').select2({
                     width: '100%', placeholder: 'Select urgency', allowClear: true, minimumResultsForSearch: Infinity,
                     dropdownCssClass: 'enhanced-dropdown', selectionCssClass: 'enhanced-selection'
                 }).on('change', () => loadFilteredData());
             } else {
                 console.warn("jQuery/Select2 not found for Urgency Filter. Using basic select.");
                 urgencyFilter.addEventListener('change', () => loadFilteredData());
             }
        } else {
            console.warn("Urgency filter element not found.");
        }

        // -- L-Level Filters (L4 & L5) --
        const lLevelContainer = document.getElementById('lLevelFilters');
        const lLevelFilterData = filterOptions.l_levels || [];

        if (lLevelContainer && Array.isArray(lLevelFilterData) && lLevelFilterData.length > 0) {
            lLevelContainer.innerHTML = '';

            lLevelFilterData.forEach(levelOption => {
                if (!levelOption || !levelOption.column || !Array.isArray(levelOption.values)) {
                    console.warn("Invalid L-Level filter option received:", levelOption);
                    return;
                }
                const levelColumn = levelOption.column;
                const levelValues = levelOption.values;

                let labelText = levelColumn;
                let placeholderText = `Select ${levelColumn}`;
                if (levelColumn === 'CMDB_L4_NAME') { labelText = 'L4 - Manager'; placeholderText = 'Select Manager'; }
                else if (levelColumn === 'CMDB_L5_NAME') { labelText = 'L5 - Team'; placeholderText = 'Select Team'; }

                const filterGroup = document.createElement('div');
                filterGroup.className = 'filter-group l-level-filter';
                const label = document.createElement('label');
                label.htmlFor = `l_level_${levelColumn}`; label.textContent = labelText;
                filterGroup.appendChild(label);
                const select = document.createElement('select');
                select.id = `l_level_${levelColumn}`; select.className = 'form-select l-level-select';
                select.dataset.columnName = levelColumn;
                const emptyOption = document.createElement('option');
                emptyOption.value = ''; emptyOption.textContent = placeholderText;
                select.appendChild(emptyOption);
                levelValues.sort().forEach(value => {
                    const option = document.createElement('option');
                    option.value = value; option.textContent = value;
                    select.appendChild(option);
                });
                filterGroup.appendChild(select);
                lLevelContainer.appendChild(filterGroup);

                if (typeof $ !== 'undefined' && $.fn.select2) {
                    $(`#${select.id}`).select2({
                        width: '100%', placeholder: placeholderText, allowClear: true, minimumResultsForSearch: 0,
                        dropdownCssClass: 'enhanced-dropdown', selectionCssClass: 'enhanced-selection',
                        templateResult: formatOption,
                        escapeMarkup: function (markup) { return markup; } // FIX ADDED
                    });
                } else {
                    console.warn(`jQuery/Select2 not found for L-Level Filter: ${select.id}. Using basic select.`);
                }
            });
        } else {
             console.warn("L-Level filter container or options not found/invalid.");
             if(lLevelContainer) lLevelContainer.innerHTML = '<p style="font-size: 0.9em; color: #666;">L-Level filters could not be loaded.</p>';
        }

        // -- Clear L-Level Button --
        const clearLLevelBtn = document.getElementById('clearLLevelFilters');
        if (clearLLevelBtn) {
            clearLLevelBtn.addEventListener('click', () => {
                resetAllLLevelFilters();
                const appOwnerInput = document.getElementById('appOwnerSearch');
                if (appOwnerInput) appOwnerInput.value = '';
                 const applyBtn = document.getElementById('applyLLevelFilters');
                 if (applyBtn) applyBtn.click(); else loadFilteredData(true);
            });
        }

        // -- Sorting Controls --
        const sortBySelect = document.getElementById('sortBySelect');
        const sortOrderSelect = document.getElementById('sortOrderSelect');
        if (sortBySelect) sortBySelect.addEventListener('change', () => loadFilteredData());
        if (sortOrderSelect) sortOrderSelect.addEventListener('change', () => loadFilteredData());

        // -- AppOwner Search --
        const appOwnerSearch = document.getElementById('appOwnerSearch');
        if (appOwnerSearch) {
            appOwnerSearch.addEventListener('input', debounce(function() {
                const searchTerm = this.value.trim();
                console.log('Busca por Dono da App alterada:', searchTerm);
                filterApplicationsByOwner(searchTerm);
                 const activeTab = document.querySelector('.tab-button.active');
                 if (activeTab && activeTab.getAttribute('data-tab') === 'enhanced-correlation') {
                     if (typeof window.loadClusterVisualization === 'function') {
                          window.loadClusterVisualization(searchTerm);
                     } else {
                          console.error("window.loadClusterVisualization is not defined.");
                     }
                 }
            }, 500));
        }

        await loadFilteredData();
        console.log("Filtros inicializados e dados iniciais carregados.");

    } catch (error) {
        console.error('Erro ao inicializar filtros:', error);
        showError('Falha ao inicializar filtros: ' + error.message, 'globalErrorContainer');
    }
}

/**
 * Sets up click handlers for frequency filter tags using event delegation.
 */
function setupFrequencyTags() {
    const tagsContainer = document.querySelector('.frequency-filters');
    if (!tagsContainer) {
        console.warn("Container de tags de frequência não encontrado."); // This might still log if HTML isn't fixed
        return;
    }
    tagsContainer.addEventListener('click', function(event) {
        const clickedTag = event.target.closest('.frequency-tag');
        if (!clickedTag || clickedTag.disabled || clickedTag.classList.contains('active')) return;

        const allTags = tagsContainer.querySelectorAll('.frequency-tag');
        const value = clickedTag.dataset.value;
        const originalContent = clickedTag.innerHTML;
        clickedTag.innerHTML = `<div class="loading-spinner" style="width: 12px; height: 12px; margin-right: 6px; display: inline-block; border-top-color: currentColor;"></div>${clickedTag.textContent}`;
        clickedTag.disabled = true;
        allTags.forEach(t => { if(t !== clickedTag) t.style.pointerEvents = 'none'; });

        currentFrequencyFilter = value;
        console.log(`Filtro de frequência alterado para: '${value}'`);

        loadFilteredData()
            .then(() => {
                allTags.forEach(t => t.classList.remove('active'));
                clickedTag.classList.add('active');
            })
            .catch(error => console.error("Erro ao carregar dados após mudança de filtro de frequência:", error))
            .finally(() => {
                clickedTag.innerHTML = originalContent;
                clickedTag.disabled = false;
                allTags.forEach(t => { t.style.pointerEvents = ''; });
            });
    });

     const initialTag = tagsContainer.querySelector('.frequency-tag[data-value=""]');
     if (initialTag) {
         initialTag.classList.add('active');
     } else {
          const firstTag = tagsContainer.querySelector('.frequency-tag');
          if(firstTag) firstTag.classList.add('active');
     }
}

/**
 * Sets up click handler for the main 'Clear Filters' button.
 */
function setupClearFilters() {
    const clearButton = document.querySelector('.clear-filters');
    if (!clearButton) { console.warn("Clear Filters button not found."); return; }
    clearButton.addEventListener('click', () => {
        const originalContent = clearButton.innerHTML;
        clearButton.innerHTML = `<div class="loading-spinner" style="width: 14px; height: 14px; margin-right: 6px; border-top-color: currentColor;"></div> Clearing...`;
        clearButton.disabled = true;

        document.querySelectorAll('.frequency-tag').forEach(t => t.classList.remove('active'));
        const allFreqTag = document.querySelector('.frequency-tag[data-value=""]');
        if (allFreqTag) allFreqTag.classList.add('active');
        currentFrequencyFilter = "";

        try {
            if (typeof $ !== 'undefined' && $.fn.select2) {
                if ($('#appFilter').data('select2')) $('#appFilter').val(null).trigger('change.select2');
                if ($('#productFilter').data('select2')) $('#productFilter').val(null).trigger('change.select2');
                if ($('#urgencyFilter').data('select2')) $('#urgencyFilter').val(null).trigger('change.select2');
            } else {
                const appFilterEl = document.getElementById('appFilter'); if (appFilterEl) appFilterEl.value = '';
                const productFilterEl = document.getElementById('productFilter'); if (productFilterEl) productFilterEl.value = '';
                const urgencyFilterEl = document.getElementById('urgencyFilter'); if (urgencyFilterEl) urgencyFilterEl.value = '';
            }
        } catch(e) { console.warn("Error resetting Select2/basic filters:", e); }

        const appOwnerInput = document.getElementById('appOwnerSearch');
        if (appOwnerInput) appOwnerInput.value = '';
        resetAllLLevelFilters();
        const sortBySelect = document.getElementById('sortBySelect'); if (sortBySelect) sortBySelect.value = 'priority';
        const sortOrderSelect = document.getElementById('sortOrderSelect'); if (sortOrderSelect) sortOrderSelect.value = 'desc';

        loadFilteredData()
            .finally(() => {
                clearButton.innerHTML = originalContent;
                clearButton.disabled = false;
            });
    });
}

/**
 * Sets up click handler for the 'Apply L-Level Filters' button.
 */
function setupApplyLLevelFilters() {
    const applyButton = document.getElementById('applyLLevelFilters');
    if (!applyButton) { console.warn("Apply L-Level Filters button not found."); return; }
    applyButton.addEventListener('click', (event) => {
        event.preventDefault();
        const originalContent = applyButton.innerHTML;
        applyButton.innerHTML = `<div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px; border-top-color: currentColor;"></div> Applying...`;
        applyButton.disabled = true;
        loadFilteredData(true)
            .finally(() => {
                applyButton.innerHTML = originalContent;
                applyButton.disabled = false;
            });
    });
}

/**
 * Resets all L-level filter dropdowns to their default (empty) state.
 */
function resetAllLLevelFilters() {
    document.querySelectorAll('.l-level-select').forEach(select => {
        try {
            if (typeof $ !== 'undefined' && $.fn.select2 && $(select).data('select2')) {
                 $(`#${select.id}`).val(null).trigger('change.select2');
            } else {
                 select.value = '';
            }
        } catch (e) {
            console.warn(`Could not reset Select2/basic for ${select.id}:`, e);
            select.value = ''; // Fallback
        }
    });
}


// --- Data Loading and Processing ---

/**
 * Fetches recommendation data from the API based on current filter settings.
 * Updates global data variables and triggers UI updates.
 * @param {boolean} includeLLevels - If true, explicitly includes selected L-level filters in the request parameters.
 */
async function loadFilteredData(includeLLevels = false) {
    showLoading(true);
    try {
        const params = new URLSearchParams();
        const frequency = currentFrequencyFilter;
        let appValue = null, productValue = null, urgencyValue = null;
        const appFilterEl = document.getElementById('appFilter');
        const productFilterEl = document.getElementById('productFilter');
        const urgencyFilterEl = document.getElementById('urgencyFilter');
        if (appFilterEl) appValue = (typeof $ !== 'undefined' && $.fn.select2 && $(appFilterEl).data('select2')) ? $('#appFilter').val() : appFilterEl.value;
        if (productFilterEl) productValue = (typeof $ !== 'undefined' && $.fn.select2 && $(productFilterEl).data('select2')) ? $('#productFilter').val() : productFilterEl.value;
        if (urgencyFilterEl) urgencyValue = (typeof $ !== 'undefined' && $.fn.select2 && $(urgencyFilterEl).data('select2')) ? $('#urgencyFilter').val() : urgencyFilterEl.value;
        const ownerSearchTerm = document.getElementById('appOwnerSearch')?.value.trim();
        const sortBy = document.getElementById('sortBySelect')?.value || 'priority';
        const sortOrder = document.getElementById('sortOrderSelect')?.value || 'desc';

        if (frequency) params.append('frequency', frequency);
        if (appValue) params.append('app', appValue);
        if (productValue) params.append('product', productValue);
        if (urgencyValue) params.append('urgency', urgencyValue);
        if (ownerSearchTerm) params.append('app_owner', ownerSearchTerm);
        params.append('sort_by', sortBy);
        params.append('sort_order', sortOrder);

        if (includeLLevels) {
            console.log("Incluindo filtros L-Level na requisição API.");
            document.querySelectorAll('.l-level-select').forEach(select => {
                let selectedValue = (typeof $ !== 'undefined' && $.fn.select2 && $(select).data('select2')) ? $(`#${select.id}`).val() : select.value;
                if (selectedValue) {
                    const levelColumnName = select.dataset.columnName;
                    if (levelColumnName) {
                         params.append(levelColumnName, selectedValue); // Send as CMDB_L4_NAME=value etc.
                         console.log(` - Added L-Level: ${levelColumnName}=${selectedValue}`);
                    } else {
                         console.warn(`data-column-name attribute missing in L-Level select: ${select.id}`);
                    }
                }
            });
        }

        const apiUrl = `/api/recommendations?${params.toString()}`;
        console.log("Fetching recommendations:", apiUrl);
        const response = await fetch(apiUrl);
        const responseBody = await response.text();
        if (!response.ok) throw new Error(`API Error: ${response.status} ${response.statusText}. Details: ${responseBody}`);
        const data = JSON.parse(responseBody);
        if (data.error) throw new Error(data.error);

        currentData = data;
        processApplicationsData(data.recommendations || {});
        updateSummary(data.summary || {});
        updateRemediationStats(data.summary?.remediation_stats || {});
        updateTopApplications(data.summary?.top_5_applications || []);
        updateIntelligentInsights(data.product_recommendations || {});

         const activeTab = document.querySelector('.tab-button.active');
         if (activeTab && activeTab.getAttribute('data-tab') === 'summary') {
            setTimeout(() => {
                createExecutionChart(data.recommendations || {});
                createAppExecutionChart(data.recommendations || {});
            }, 0);
         }

        const activeTabId = activeTab?.getAttribute('data-tab');
        const activePane = document.getElementById(`${activeTabId}-tab`);
        if (activePane) {
            setTimeout(() => {
                try {
                     if (activeTabId === 'org-funnel' && activePane.querySelector('.funnel-container')) updateOrganizationalRecommendations(data.metadata?.org_recommendations);
                     else if (activeTabId === 'product-rankings' && activePane.querySelector('.product-rankings')) updateProductRecommendations(data.metadata?.product_recommendations);
                } catch(e) { console.error(`Erro ao atualizar conteúdo para aba ativa ${activeTabId}:`, e); }
            }, 50);
        }

        currentPage = 1;
        displayApplicationCards(currentPage);

    } catch (error) {
        console.error('Error loading filtered data:', error);
        showError('Failed to load data: ' + error.message, 'globalErrorContainer');
        const appContainer = document.getElementById('applicationsContainer');
        if (appContainer) appContainer.innerHTML = '<div class="error-message" style="padding: 20px;">Could not load application data.</div>';
        if (executionChart instanceof Chart) { try { executionChart.destroy(); } catch(e){} }
        if (appExecutionChart instanceof Chart) { try { appExecutionChart.destroy(); } catch(e){} }
        executionChart = null; appExecutionChart = null;
        updateSummary({});
        updatePagination();
    } finally {
        showLoading(false);
    }
}


/**
 * Processes the raw recommendation data from the API into the structured
 * `allApplications` array used for displaying cards. Also runs L-level analysis.
 * @param {object} recommendations - The recommendations object from the API response.
 */
function processApplicationsData(recommendations) {
    allApplications = [];
    if (!recommendations || typeof recommendations !== 'object') {
        console.warn("Invalid recommendation data received for processing.");
        filteredApplications = null; filterProductsBySameLLevel(); return;
    }
    Object.entries(recommendations).forEach(([ownerKey, ownerData]) => {
        if (!ownerData || typeof ownerData !== 'object' || !Array.isArray(ownerData.apps)) return;
        ownerData.apps.forEach(app => {
            if (!app || typeof app !== 'object') return;
            const appLLevel = app.l_level || 'N/A';
            const priorityScore = typeof app.priority_score === 'number' ? app.priority_score : 0;
            allApplications.push({
                name: app.name || 'Unnamed App',
                owner: ownerData.owner || ownerKey || 'Unknown Owner',
                priorityScore: priorityScore,
                servers: Array.isArray(app.servers) ? app.servers.length : 0,
                remediatedVulnerabilities: getRemediatedVulnerabilities(app),
                executionRate: getExecutionRate(priorityScore),
                mostCommonProduct: app.most_common_product || 'N/A',
                mostCommonOperation: app.most_common_operation || 'N/A',
                lLevel: appLLevel,
                productUsage: extractProductUsageRanking(app)
            });
        });
    });
    allApplications.sort((a, b) => b.priorityScore - a.priorityScore);
    const ownerSearchTerm = document.getElementById('appOwnerSearch')?.value.trim();
    if (ownerSearchTerm) filterApplicationsByOwner(ownerSearchTerm); else filteredApplications = null;
    filterProductsBySameLLevel();
    console.log(`Processed ${allApplications.length} applications.`);
}

/**
 * Filters the `allApplications` array based on the owner/app name search term
 * and updates the `filteredApplications` global variable. Then redraws the cards.
 * @param {string} searchTerm - The term to filter by.
 */
function filterApplicationsByOwner(searchTerm) {
    const container = document.getElementById('applicationsContainer');
    const lowerSearchTerm = searchTerm.toLowerCase();
    if (!searchTerm) filteredApplications = null; else {
        filteredApplications = allApplications.filter(app =>
            (app.owner && app.owner.toLowerCase().includes(lowerSearchTerm)) ||
            (app.name && app.name.toLowerCase().includes(lowerSearchTerm))
        );
        console.log(`Filtering by owner/name: "${searchTerm}", found ${filteredApplications.length}`);
    }
    currentPage = 1;
    displayApplicationCards(currentPage);
    if (container) {
        let noResultsMsg = document.getElementById('noAppsMessage');
        if (filteredApplications && filteredApplications.length === 0 && searchTerm) {
            if (!noResultsMsg) {
                if (!container.querySelector('#noAppsMessage')) container.insertAdjacentHTML('beforeend', `<div id="noAppsMessage" class="no-results-message" style="padding: 20px; text-align: center;"></div>`);
                noResultsMsg = document.getElementById('noAppsMessage');
            }
            if (noResultsMsg) { noResultsMsg.textContent = `No applications found matching "${searchTerm}".`; noResultsMsg.style.display = 'block'; }
        } else if (noResultsMsg) { noResultsMsg.style.display = 'none'; }
    }
}


/**
 * Calculates the count of remediated vulnerabilities based on success statuses.
 * Case-insensitive check for 'success'.
 */
function getRemediatedVulnerabilities(app) {
    if (!app || typeof app.status_summary !== 'object' || app.status_summary === null) return 0;
    return Object.entries(app.status_summary).reduce((count, [status, num]) =>
        (typeof status === 'string' && status.toLowerCase().includes('success')) ? count + (Number(num) || 0) : count, 0);
}

/**
 * Determines the execution rate category text and level based on priority score.
 * Returns an object: { level: string, text: string }
 */
function getExecutionRate(score) {
    score = Number(score) || 0;
    if (score >= 5.0) return { level: 'high', text: 'High Fidelity Client' };
    if (score >= 3.0) return { level: 'medium', text: 'Frequent User' };
    if (score >= 1.0) return { level: 'occasional', text: 'Occasional' };
    return { level: 'low', text: 'Low Frequency' };
}

/**
 * Extracts and ranks product usage from server data within an application object.
 * Returns an array of objects: { name: string, executions: number, successRate: number }[]
 * Returns mock data if server data is unavailable or invalid.
 */
function extractProductUsageRanking(app) {
    if (!app || !Array.isArray(app.servers) || app.servers.length === 0) {
        const mockData = [], potentialProducts = ["Chrome", "MS Edge", "Java", "Python", "IIS", "Security Headers", "Tomcat", "Apache"];
        let usedProducts = new Set();
        if (app?.most_common_product && app.most_common_product !== 'N/A') {
            mockData.push({ name: app.most_common_product, executions: Math.floor(Math.random() * 20) + 5, successRate: Math.floor(Math.random() * 40) + 50 });
            usedProducts.add(app.most_common_product);
        }
        for (let i = 0; i < 2 && mockData.length < 3; i++) {
             let randomProduct, attempts = 0;
             do { randomProduct = potentialProducts[Math.floor(Math.random() * potentialProducts.length)]; attempts++; } while (usedProducts.has(randomProduct) && attempts < potentialProducts.length * 2);
             if (!usedProducts.has(randomProduct)) { mockData.push({ name: randomProduct, executions: Math.floor(Math.random() * 10) + 3, successRate: Math.floor(Math.random() * 50) + 40 }); usedProducts.add(randomProduct); }
        }
        return mockData.sort((a, b) => b.executions - a.executions);
    }
    const productStats = {};
    app.servers.forEach(server => {
        if (!server || !Array.isArray(server.products)) return;
        const isSuccess = typeof server.status === 'string' && server.status.toLowerCase().includes('success');
        server.products.forEach(productName => {
            if (!productName || typeof productName !== 'string') return;
            if (!productStats[productName]) productStats[productName] = { executions: 0, successes: 0 };
            productStats[productName].executions++;
            if (isSuccess) productStats[productName].successes++;
        });
    });
    const rankedProducts = Object.entries(productStats).map(([name, stats]) => ({ name, executions: stats.executions, successRate: stats.executions > 0 ? Math.round((stats.successes / stats.executions) * 100) : 0 }));
    rankedProducts.sort((a, b) => (b.executions !== a.executions) ? b.executions - a.executions : a.name.localeCompare(b.name));
    return rankedProducts;
}


// --- UI Display Functions ---

/**
 * Renders application cards into the container based on the current page.
 * Uses `filteredApplications` if available, otherwise `allApplications`.
 * @param {number} page - The page number to display (1-based).
 */
function displayApplicationCards(page = 1) {
    const container = document.getElementById('applicationsContainer');
    if (!container) { console.error('Applications container not found!'); return; }
    container.innerHTML = '';
    const appsToDisplay = filteredApplications !== null ? filteredApplications : allApplications;
    console.log(`[Display Cards] Displaying page ${page}. Filtered: ${filteredApplications !== null}. Total apps: ${appsToDisplay?.length ?? 0}`);

    if (!appsToDisplay || appsToDisplay.length === 0) {
        const ownerSearchTerm = document.getElementById('appOwnerSearch')?.value.trim();
        const isFiltered = filteredApplications !== null;
        let message = "No application data available.";
        if (isFiltered && ownerSearchTerm) message = `No applications found matching owner/name "${ownerSearchTerm}".`;
        else if (isFiltered) message = "No applications match the current filters.";
        let noResultsMsg = document.getElementById('noAppsMessage');
         if (!noResultsMsg) { if (!container.querySelector('#noAppsMessage')) container.insertAdjacentHTML('beforeend', `<div id="noAppsMessage" class="no-results-message" style="padding: 20px; text-align: center;"></div>`); noResultsMsg = document.getElementById('noAppsMessage'); }
         if(noResultsMsg) { noResultsMsg.textContent = message; noResultsMsg.style.display = 'block'; }
        updatePagination(); return;
    } else {
         const noResultsMsg = document.getElementById('noAppsMessage'); if (noResultsMsg) noResultsMsg.style.display = 'none';
    }

    const totalItems = appsToDisplay.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
    page = Math.max(1, Math.min(page, totalPages)); currentPage = page;
    const start = (page - 1) * itemsPerPage, end = start + itemsPerPage;
    const paginatedApps = appsToDisplay.slice(start, end);
    const fragment = document.createDocumentFragment();

    paginatedApps.forEach(app => {
        const card = document.createElement('div'); card.className = 'application-card';
        let appName = app.name || 'Unnamed App', appId = '';
        const idMatch = appName.match(/\s*\(([^)]+)\)$/);
        if (idMatch && idMatch[1]) { appId = idMatch[1]; appName = appName.replace(/\s*\(([^)]+)\)$/, '').trim(); }
        const lLevelTag = (app.lLevel && app.lLevel !== 'N/A') ? `<span class="l-level-tag" title="L-Level: ${app.lLevel}">${app.lLevel}</span>` : '';
        const executionBadge = `<span class="execution-badge ${app.executionRate.level}" title="Execution Rate based on Priority Score">${app.executionRate.text}</span>`;
        let productRankingHTML = '';
        if (app.productUsage && app.productUsage.length > 0) {
            productRankingHTML = `<div class="product-usage-ranking"><div class="product-header">Product Usage (Top ${Math.min(3, app.productUsage.length)})</div>${app.productUsage.slice(0, 3).map((product, index) => `<div class="usage-rank-item"><div class="usage-rank-number">${index + 1}</div><div class="usage-rank-details"><div class="usage-rank-name" title="${product.name}">${product.name}</div><div class="usage-rank-executions">${product.executions} executions (${product.successRate.toFixed(0)}% success)</div></div></div>`).join('')}</div>`;
        } else { productRankingHTML = `<div class="product-usage-ranking"><div class="product-header">Product Usage</div><div class="no-data-message" style="font-size: 0.8em;">No usage data</div></div>`; }
        card.innerHTML = `<div class="card-header"><div class="application-title" title="${app.name}">${appName} ${appId ? `<span class="app-id">(${appId})</span>` : ''}</div><div class="application-owner" title="Owner: ${app.owner}">${app.owner} ${lLevelTag}</div></div><div class="card-body"><div class="priority-section"><div class="priority-score" title="Priority Score">${app.priorityScore.toFixed(2)}</div><div class="priority-label">Priority Score ${executionBadge}</div></div><div class="app-metrics-grid"><div class="metric-item"><div class="metric-value" title="Successful Remediations">${app.remediatedVulnerabilities}</div><div class="metric-label">Remediations</div></div><div class="metric-item"><div class="metric-value" title="Associated Servers">${app.servers}</div><div class="metric-label">Servers</div></div></div><div class="product-section"><div class="product-info"><span class="product-label">Main Product:</span><span class="product-value" title="${app.mostCommonProduct}">${app.mostCommonProduct || 'N/A'}</span></div><div class="product-info"><span class="product-label">Main Operation:</span><span class="product-value" title="${app.mostCommonOperation}">${app.mostCommonOperation || 'N/A'}</span></div></div>${productRankingHTML}</div>`;
        fragment.appendChild(card);
    });
    container.appendChild(fragment);
    updatePagination();
}


/**
 * Updates the pagination controls (page numbers, button states).
 */
function updatePagination() {
    const apps = filteredApplications !== null ? filteredApplications : allApplications;
    const totalItems = apps ? apps.length : 0;
    const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
    if (totalItems === 0) currentPage = 1; else currentPage = Math.max(1, Math.min(currentPage, totalPages));
    const paginationControls = [ { currentId: 'currentPage', totalId: 'totalPages', prevId: 'prevPage', nextId: 'nextPage', containerClass: '.pagination-controls-top' }, { currentId: 'currentPageBottom', totalId: 'totalPagesBottom', prevId: 'prevPageBottom', nextId: 'nextPageBottom', containerClass: '.pagination-controls-bottom' } ];
    paginationControls.forEach(controlSet => {
        const currentPageEl = document.getElementById(controlSet.currentId); const totalPagesEl = document.getElementById(controlSet.totalId);
        const prevPageEl = document.getElementById(controlSet.prevId); const nextPageEl = document.getElementById(controlSet.nextId);
        const container = document.querySelector(controlSet.containerClass);
        if (currentPageEl) currentPageEl.textContent = currentPage; if (totalPagesEl) totalPagesEl.textContent = totalPages;
        if (prevPageEl) prevPageEl.disabled = (currentPage <= 1); if (nextPageEl) nextPageEl.disabled = (currentPage >= totalPages);
        if (container) container.style.display = (totalPages <= 1) ? 'none' : '';
    });
}


/**
 * Sets up event listeners for the pagination buttons (top and bottom).
 */
function setupPaginationListeners() {
    const handlePageChange = (direction) => {
        const apps = filteredApplications !== null ? filteredApplications : allApplications; if (!apps) return;
        const totalPages = Math.ceil(apps.length / itemsPerPage) || 1; const newPage = currentPage + direction;
        if (newPage >= 1 && newPage <= totalPages) displayApplicationCards(newPage);
    };
    document.getElementById('prevPage')?.addEventListener('click', () => handlePageChange(-1));
    document.getElementById('nextPage')?.addEventListener('click', () => handlePageChange(1));
    document.getElementById('prevPageBottom')?.addEventListener('click', () => handlePageChange(-1));
    document.getElementById('nextPageBottom')?.addEventListener('click', () => handlePageChange(1));
}


/**
 * Updates the summary information display area.
 * @param {object} summary - The summary object from the API data.
 */
function updateSummary(summary) {
     const updateText = (id, value, defaultValue = '0') => { const el = document.getElementById(id); if (el) el.textContent = (value !== null && value !== undefined) ? value : defaultValue; };
     const updateHTML = (id, html, defaultValue = 'N/A') => { const el = document.getElementById(id); if (el) el.innerHTML = (html !== null && html !== undefined && html !== '') ? html : defaultValue; };
    if (!summary || typeof summary !== 'object' || Object.keys(summary).length === 0) {
        updateText('summaryTotalOwners', '0'); updateText('summaryTotalApps', '0'); updateText('summaryTotalServers', '0');
        updateHTML('summaryEnvironments', 'N/A'); updateHTML('summaryProducts', 'N/A'); return;
    }
    updateText('summaryTotalOwners', summary.total_owners); updateText('summaryTotalApps', summary.total_apps); updateText('summaryTotalServers', summary.total_servers);
    const envSummaryHTML = (summary.environment_distribution && typeof summary.environment_distribution === 'object') ? Object.entries(summary.environment_distribution).map(([env, count]) => `<div>${env}: ${count}</div>`).join('') : null;
    updateHTML('summaryEnvironments', envSummaryHTML);
    const productSummaryHTML = (summary.most_common_products && typeof summary.most_common_products === 'object') ? Object.entries(summary.most_common_products).sort(([, countA], [, countB]) => countB - countA).slice(0, 5).map(([product, count]) => `<div>${product}: ${count}</div>`).join('') : null;
    updateHTML('summaryProducts', productSummaryHTML);
}

/**
 * Updates the remediation statistics section.
 * @param {object} remediationStats - The remediation stats object from the API response.
 */
function updateRemediationStats(remediationStats) {
    const updateText = (id, value, defaultValue = '0') => {
        const el = document.getElementById(id);
        if (el) el.textContent = (value !== null && value !== undefined) ? value : defaultValue;
    };

    if (!remediationStats || typeof remediationStats !== 'object') {
        updateText('remediationTotalItems', '0');
        updateText('remediationRemediated', '0');
        updateText('remediationPending', '0');
        updateText('remediationSuccessRate', '0%');
        updateText('remediationFailed', '0');
        updateText('remediationUnknown', '0');
        return;
    }

    updateText('remediationTotalItems', remediationStats.total_items);
    updateText('remediationRemediated', remediationStats.remediated_count);
    updateText('remediationPending', remediationStats.pending_count);
    updateText('remediationSuccessRate', `${remediationStats.success_rate || 0}%`);
    updateText('remediationFailed', remediationStats.failed_count);
    updateText('remediationUnknown', remediationStats.unknown_count);
}

/**
 * Updates the top 5 applications section.
 * @param {Array} topApplications - Array of top application objects from the API response.
 */
function updateTopApplications(topApplications) {
    const container = document.getElementById('topApplicationsList');
    if (!container) return;

    if (!Array.isArray(topApplications) || topApplications.length === 0) {
        container.innerHTML = '<div class="no-data-message">No application data available.</div>';
        return;
    }

    const html = topApplications.map((app, index) => `
        <div class="top-app-item">
            <div class="top-app-rank">${index + 1}</div>
            <div class="top-app-details">
                <div class="top-app-name">${app.name || 'Unknown App'}</div>
                <div class="top-app-owner">Owner: ${app.owner || 'Unknown'}</div>
                <div class="top-app-metrics">
                    <span class="metric">Total: ${app.total_items || 0}</span>
                    <span class="metric">Remediated: ${app.remediated_items || 0}</span>
                    <span class="metric">Pending: ${app.pending_items || 0}</span>
                    <span class="metric">Success Rate: ${app.success_rate || 0}%</span>
                    <span class="metric">Priority: ${app.avg_priority_score || 0}</span>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

/**
 * Updates the intelligent insights section with AI-driven recommendations.
 * @param {object} productRecommendations - The product recommendations object from the API response.
 */
function updateIntelligentInsights(productRecommendations) {
    const container = document.getElementById('intelligentInsights');
    if (!container) return;

    // Check if we have intelligent recommendations
    if (!productRecommendations ||
        !productRecommendations.intelligent_recommendations ||
        !Array.isArray(productRecommendations.intelligent_recommendations) ||
        productRecommendations.intelligent_recommendations.length === 0) {

        container.innerHTML = '<div class="no-insights">No intelligent insights available at this time.</div>';
        return;
    }

    const recommendations = productRecommendations.intelligent_recommendations;

    const html = recommendations.map(rec => {
        const priorityClass = rec.priority || 'medium';
        const confidencePercent = Math.round((rec.confidence_score || 0) * 100);

        // Generate specific action items HTML (concise version)
        let specificActionsHtml = '';
        if (rec.specific_action_items && rec.specific_action_items.length > 0) {
            // Limit to top 3 action items for conciseness
            const topActions = rec.specific_action_items.slice(0, 3);
            specificActionsHtml = `
                <div class="specific-actions">
                    <div class="specific-actions-title">Key Actions:</div>
                    ${topActions.map(action => `
                        <div class="action-item-compact">
                            <div class="action-summary">
                                <strong>${action.target_application}</strong> → ${action.recommended_product}
                                <span class="action-impact">(${action.estimated_impact_servers} servers, +${action.expected_success_rate_improvement.toFixed(1)}%)</span>
                            </div>
                        </div>
                    `).join('')}
                    ${rec.specific_action_items.length > 3 ? `<div class="more-actions">+${rec.specific_action_items.length - 3} more actions</div>` : ''}
                </div>
            `;
        }

        return `
            <div class="insight-card-compact ${priorityClass}">
                <div class="insight-header-compact">
                    <div class="insight-title-compact">${rec.title || 'Recommendation'}</div>
                    <div class="insight-meta">
                        <span class="insight-priority ${priorityClass}">${priorityClass.toUpperCase()}</span>
                        <span class="insight-confidence">${confidencePercent}%</span>
                    </div>
                </div>

                <div class="insight-description-compact">
                    ${rec.description || 'No description available.'}
                </div>

                <div class="insight-summary">
                    <div class="insight-impact">
                        <strong>Impact:</strong> ${rec.potential_impact || 'Not specified'}
                    </div>
                    ${rec.implementation_timeline ? `
                    <div class="insight-timeline">
                        <strong>Timeline:</strong> ${rec.implementation_timeline}
                    </div>
                    ` : ''}
                </div>

                ${rec.suggested_actions && rec.suggested_actions.length > 0 ? `
                <div class="insight-actions-compact">
                    <div class="insight-actions-title">Actions:</div>
                    <ul class="insight-actions-list-compact">
                        ${rec.suggested_actions.slice(0, 4).map(action => `<li>• ${action}</li>`).join('')}
                        ${rec.suggested_actions.length > 4 ? `<li class="more-actions">• +${rec.suggested_actions.length - 4} more</li>` : ''}
                    </ul>
                </div>
                ` : ''}

                ${specificActionsHtml}
            </div>
        `;
    }).join('');

    container.innerHTML = html;
}

// --- Chart Creation ---

/**
 * Creates or updates the Client Execution Analysis chart (Owners vs Apps/Rate).
 */
function createExecutionChart(recommendations) {
    const ctxElement = document.getElementById('executionChart'); if (!ctxElement) { console.warn("Elemento canvas 'executionChart' não encontrado."); return; }
    const ctx = ctxElement.getContext('2d'); if (!ctx) { console.error("Falha ao obter contexto 2D para 'executionChart'"); return; }
    if (executionChart instanceof Chart) { try { executionChart.destroy(); } catch(e) { console.error("Erro ao destruir executionChart anterior:", e); } executionChart = null; }
    let sortedOwners = [];
    if (recommendations && typeof recommendations === 'object') {
         const owners = Object.values(recommendations).filter(o => o && typeof o === 'object');
         sortedOwners = [...owners].sort((a, b) => (b?.total_apps || 0) - (a?.total_apps || 0)).slice(0, 15);
    } else { console.warn("Nenhum dado de recomendação válido para o Gráfico de Execução."); ctx.clearRect(0, 0, ctxElement.width, ctxElement.height); return; }
    const labels = sortedOwners.map(item => { const name = item?.owner || 'Unknown'; return name.length > 20 ? name.substring(0, 17) + '...' : name; });
    const appCounts = sortedOwners.map(item => item?.total_apps || 0);
    const executionRates = sortedOwners.map(item => { if (!item || !Array.isArray(item.apps) || item.apps.length === 0) return 0; const totalScore = item.apps.reduce((sum, app) => sum + (Number(app?.priority_score) || 0), 0); return item.apps.length > 0 ? (totalScore / item.apps.length) : 0; });
    const maxApps = appCounts.length > 0 ? Math.max(...appCounts) : 0;
    try {
        executionChart = new Chart(ctx, {
            type: 'bar', data: { labels: labels, datasets: [ { label: 'Número de Aplicações', data: appCounts, backgroundColor: 'rgba(0, 118, 206, 0.7)', borderColor: 'rgba(0, 118, 206, 1)', borderWidth: 1, yAxisID: 'yApps', order: 2 }, { label: 'Taxa Média de Execução', data: executionRates, type: 'line', borderColor: 'rgba(45, 106, 79, 1)', backgroundColor: 'rgba(45, 106, 79, 0.1)', borderWidth: 2, pointBackgroundColor: 'rgba(45, 106, 79, 1)', pointRadius: 3, fill: false, yAxisID: 'yRate', order: 1 } ] },
            options: { responsive: true, maintainAspectRatio: false, interaction: { mode: 'index', intersect: false, }, scales: { x: { ticks: { autoSkip: false, maxRotation: 45, minRotation: 45 }, grid: { display: false } }, yApps: { type: 'linear', display: true, position: 'left', title: { display: true, text: 'Número de Aplicações' }, beginAtZero: true, suggestedMax: maxApps > 0 ? Math.ceil(maxApps * 1.1 / 5) * 5 : 10 }, yRate: { type: 'linear', display: true, position: 'right', title: { display: true, text: 'Taxa Média de Execução (Prioridade)' }, min: 0, max: 7, beginAtZero: true, grid: { drawOnChartArea: false, } } }, plugins: { tooltip: { callbacks: { title: (tooltipItems) => { const index = tooltipItems[0]?.dataIndex; return (index !== undefined && sortedOwners[index]) ? sortedOwners[index].owner || 'Dono Desconhecido' : ''; }, label: (context) => { let label = context.dataset.label || ''; if (label) label += ': '; if (context.parsed.y !== null) label += (context.dataset.yAxisID === 'yRate') ? context.parsed.y.toFixed(2) : context.parsed.y; return label; } } }, legend: { display: true, position: 'top' } } }
        });
    } catch (e) { console.error("Erro ao criar Gráfico de Execução:", e); showError("Não foi possível exibir o Gráfico de Execução do Proprietário.", ctxElement.id); executionChart = null; }
}


/**
 * Creates or updates the Application Execution Analysis chart.
 */
function createAppExecutionChart(recommendations) {
    const ctxElement = document.getElementById('appExecutionChart'); if (!ctxElement) { console.warn("Elemento canvas 'appExecutionChart' não encontrado."); return; }
    const ctx = ctxElement.getContext('2d'); if (!ctx) { console.error("Falha ao obter contexto 2D para 'appExecutionChart'"); return; }
    if (appExecutionChart instanceof Chart) { try { appExecutionChart.destroy(); } catch(e) { console.error("Erro ao destruir appExecutionChart anterior:", e); } appExecutionChart = null; }
    let allAppsData = [];
    if (recommendations && typeof recommendations === 'object') {
        Object.values(recommendations).forEach(owner => { if (owner && Array.isArray(owner.apps)) { owner.apps.forEach(app => { if (app && typeof app === 'object') { const remediated = getRemediatedVulnerabilities(app); const failed = Number(app.status_summary?.FAILED) || 0; const totalVulnCount = remediated + failed; allAppsData.push({ name: app.name || 'App Desconhecido', owner: owner.owner || 'Dono Desconhecido', score: Number(app.priority_score) || 0, vulnCount: totalVulnCount, servers: Array.isArray(app.servers) ? app.servers.length : 0 }); } }); } });
    } else { console.warn("Nenhum dado de recomendação válido para o Gráfico de Execução de App."); ctx.clearRect(0, 0, ctxElement.width, ctxElement.height); return; }
    const sortedApps = [...allAppsData].sort((a, b) => b.score - a.score).slice(0, 15);
    const labels = sortedApps.map(app => { const name = app?.name || 'Unknown'; return name.length > 20 ? name.substring(0, 17) + '...' : name; });
    const serverCounts = sortedApps.map(app => app?.servers || 0); const executionScores = sortedApps.map(app => app?.score || 0); const vulnerabilityCounts = sortedApps.map(app => app?.vulnCount || 0);
    const maxServers = serverCounts.length > 0 ? Math.max(...serverCounts) : 0; const maxVulns = vulnerabilityCounts.length > 0 ? Math.max(...vulnerabilityCounts) : 0;
    try {
        appExecutionChart = new Chart(ctx, {
            type: 'bar', data: { labels: labels, datasets: [ { label: 'Número de Servidores', data: serverCounts, backgroundColor: 'rgba(24, 100, 171, 0.7)', borderColor: 'rgba(24, 100, 171, 1)', borderWidth: 1, yAxisID: 'yServers', order: 3 }, { label: 'Vulnerabilidades Totais', data: vulnerabilityCounts, backgroundColor: 'rgba(217, 30, 24, 0.7)', borderColor: 'rgba(217, 30, 24, 1)', borderWidth: 1, yAxisID: 'yVulns', order: 2 }, { label: 'Taxa de Execução (Prioridade)', data: executionScores, type: 'line', borderColor: 'rgba(45, 106, 79, 1)', backgroundColor: 'rgba(45, 106, 79, 0.1)', borderWidth: 2, pointBackgroundColor: 'rgba(45, 106, 79, 1)', pointRadius: 3, fill: false, yAxisID: 'yRate', order: 1 } ] },
            options: { responsive: true, maintainAspectRatio: false, interaction: { mode: 'index', intersect: false }, scales: { x: { ticks: { autoSkip: false, maxRotation: 45, minRotation: 45 }, grid: { display: false } }, yServers: { type: 'linear', display: true, position: 'left', title: { display: true, text: 'Número de Servidores' }, beginAtZero: true, suggestedMax: maxServers > 0 ? Math.ceil(maxServers * 1.1 / 5) * 5 : 10, grid: { color: 'rgba(0, 0, 0, 0.1)' } }, yVulns: { type: 'linear', display: true, position: 'left', title: { display: true, text: 'Vulnerabilidades Totais' }, beginAtZero: true, offset: true, suggestedMax: maxVulns > 0 ? Math.ceil(maxVulns * 1.1 / 10) * 10 : 10, grid: { drawOnChartArea: false }, ticks: { color: 'rgba(217, 30, 24, 1)' } }, yRate: { type: 'linear', display: true, position: 'right', title: { display: true, text: 'Taxa de Execução (Prioridade)' }, min: 0, max: 7, beginAtZero: true, grid: { drawOnChartArea: false } } }, plugins: { tooltip: { callbacks: { title: (tooltipItems) => { const index = tooltipItems[0]?.dataIndex; if (index !== undefined && sortedApps[index]) { const appData = sortedApps[index]; return `${appData?.name || 'Unknown'} (${appData?.owner || 'Unknown'})`; } return ''; }, label: (context) => { let label = context.dataset.label || ''; if (label) label += ': '; if (context.parsed.y !== null) label += (context.dataset.yAxisID === 'yRate') ? context.parsed.y.toFixed(2) : context.parsed.y; return label; } } }, legend: { display: true, position: 'top' } } }
        });
    } catch (e) { console.error("Erro ao criar Gráfico de Execução de App:", e); showError("Não foi possível exibir o Gráfico de Execução da Aplicação.", ctxElement.id); appExecutionChart = null; }
}


// --- Specific Content Section Updaters ---

/**
 * Fetches and displays cluster visualization data with pan/zoom/drag.
 * Includes creation of a fullscreen toggle button.
 * Optimized tick handler for better performance.
 *
 * NOTE: Assumes external functions 'createClusterControls', 'toggleClusterFullscreen',
 * 'createClusterLegend' exist and required CSS is loaded.
 */
window.loadClusterVisualization = function(appOwner = "") {
    const container = document.getElementById('clusterVisualizationContainer');
    if (!container) { console.warn("Cluster visualization container ('clusterVisualizationContainer') not found."); return; }

    // --- Limpeza e Configuração Inicial ---
    if (window.clusterVizSvg) { window.clusterVizSvg.remove(); console.log("[ClusterViz] Previous SVG removed."); }
    const oldFullscreenBtn = document.getElementById('fullscreen-toggle-btn');
    if (oldFullscreenBtn) { oldFullscreenBtn.remove(); console.log("[ClusterViz] Previous fullscreen button removed."); }

    window.clusterVizSvg = null; window.clusterVizSvgGroup = null; window.clusterVizZoom = null;
    window.clusterVizSimulation = null; window.clusterVizCurrentTransform = d3.zoomIdentity;
    window.isClusterFullscreen = false;

    const existingControls = container.querySelector('.cluster-controls');
    container.innerHTML = '';
    if (existingControls) container.appendChild(existingControls);
    else { if (typeof createClusterControls === 'function') { console.log("[ClusterViz] Creating cluster controls."); createClusterControls(container); } else console.error("createClusterControls function is not defined."); }

    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-spinner'; loadingIndicator.style.margin = '50px auto';
    loadingIndicator.style.textAlign = 'center'; loadingIndicator.textContent = ' Carregando visualização do cluster...';
    container.appendChild(loadingIndicator); console.log("[ClusterViz] Loading indicator added.");

    // --- Criação do SVG e Configuração do Zoom ---
    const width = container.clientWidth > 0 ? container.clientWidth : 600;
    const height = container.clientHeight > 0 ? container.clientHeight : 500;
    console.log(`[ClusterViz] Container dimensions for SVG: ${width}x${height}`);

    window.clusterVizSvg = d3.create("svg")
        .attr("width", "100%").attr("height", "100%").attr("viewBox", [0, 0, width, height])
        .attr("preserveAspectRatio", "xMidYMid meet").attr("class", "cluster-svg")
        .style("border", "1px solid #ccc").style("background-color", "#f9f9f9").style("display", "block")
        .style("max-width", "100%").style("cursor", "grab").style("pointer-events", "all");

    window.clusterVizSvgGroup = window.clusterVizSvg.append("g").attr("class", "zoom-group");

    window.clusterVizZoom = d3.zoom().scaleExtent([0.1, 8])
        .on("zoom", (event) => { if (window.clusterVizSvgGroup) window.clusterVizSvgGroup.attr("transform", event.transform); window.clusterVizCurrentTransform = event.transform; if (window.clusterVizSvg) window.clusterVizSvg.style("cursor", event.transform === d3.zoomIdentity ? "grab" : "grabbing"); })
        .on("end", (event) => { if (window.clusterVizSvg) window.clusterVizSvg.style("cursor", "grab"); });

    window.clusterVizSvg.call(window.clusterVizZoom);
    container.appendChild(window.clusterVizSvg.node());
    loadingIndicator.remove(); console.log("[ClusterViz] SVG created and added to container.");

    // --- CRIAÇÃO DO BOTÃO FULLSCREEN ---
    const fullscreenBtn = document.createElement('button');
    fullscreenBtn.id = 'fullscreen-toggle-btn'; fullscreenBtn.title = 'Alternar Tela Cheia';
    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>'; // Requires Font Awesome
    if (typeof toggleClusterFullscreen === 'function') fullscreenBtn.addEventListener('click', toggleClusterFullscreen);
    else { console.error("toggleClusterFullscreen function is not defined. Fullscreen button will not work."); fullscreenBtn.disabled = true; fullscreenBtn.title = 'Funcionalidade indisponível'; }
    container.appendChild(fullscreenBtn); console.log("[ClusterViz] Fullscreen button added.");

    // --- Busca e Renderização dos Dados ---
    const url = `/api/clusters?app_owner=${encodeURIComponent(appOwner)}`;
    console.log(`[ClusterViz] Requesting cluster data for app_owner="${appOwner}" from ${url}`);
    const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error("Cluster API request timed out (15s).")), 15000));

    Promise.race([ fetch(url).then(response => { if (!response.ok) { return response.text().then(text => { throw new Error(`Cluster API Error: ${response.status} ${response.statusText}. Details: ${text}`); }); } return response.json(); }), timeoutPromise ])
    .then(data => {
        console.log("[ClusterViz] Data received from API.");
        let hasNodes = data && Array.isArray(data.nodes) && data.nodes.length > 0;
        if (!hasNodes) { console.warn("[ClusterViz] No nodes found in cluster data."); window.clusterVizSvgGroup?.append("text").attr("x", width / 2).attr("y", height / 2).attr("text-anchor", "middle").attr("class", "no-data-message").text("Nenhum dado de cluster disponível para este filtro."); return; }
        console.log(`[ClusterViz] Rendering ${data.nodes.length} nodes and ${data.links?.length || 0} links.`);

        const colorScale = d3.scaleOrdinal().domain(['product_patched', 'product_unpatched', 'app', 'owner', 'llevel', 'unknown']).range(['#28a745', '#dc3545', '#007bff', '#ffc107', '#6f42c1', '#888']);
        const linkGroup = window.clusterVizSvgGroup.append("g").attr("class", "links").attr("stroke", "#aaa").attr("stroke-opacity", 0.5);
        const links = linkGroup.selectAll("line").data(data.links || []).join("line").attr("stroke-width", d => Math.max(1, Math.sqrt(d.value || 1)));
        const nodeGroup = window.clusterVizSvgGroup.append("g").attr("class", "nodes").attr("stroke", "#fff").attr("stroke-width", 1.5);
        const nodes = nodeGroup.selectAll("path").data(data.nodes).join("path")
            .attr("d", d => { const nt=d.type||'unknown', s=(nt==='owner'||nt==='llevel')?300:(nt==='app'?250:150); let st=d3.symbolCircle; if(nt==="owner"||nt==="app")st=d3.symbolSquare; else if(nt==="llevel")st=d3.symbolDiamond; return d3.symbol().type(st).size(s)(); })
            .attr("fill", d => { let ct=d.type||'unknown'; if(d.type==='product')ct=`product_${d.status||'unknown'}`; return colorScale(ct); })
            .style("cursor", "pointer").call(d3.drag().on("start", dragStart).on("drag", dragging).on("end", dragEnd));
        nodes.append("title").text(d => `${d.name||'Unnamed Node'}\nTipo: ${d.type||'N/A'}\n${d.type==='product'?`Status: ${d.status||'N/A'}`:''}`);
        const labelGroup = window.clusterVizSvgGroup.append("g").attr("class", "labels");
        const labels = labelGroup.selectAll("text").data(data.nodes).join("text").attr("x", 12).attr("y", 4).text(d => d.name||'').attr("font-size", "10px").attr("fill", "#333").attr("paint-order", "stroke").attr("stroke", "rgba(255, 255, 255, 0.7)").attr("stroke-width", "2px").style("pointer-events", "none");

        console.log("[ClusterViz] Setting up force simulation.");
        window.clusterVizSimulation = d3.forceSimulation(data.nodes)
            .force("link", d3.forceLink(data.links||[]).id(d=>d.id).distance(90).strength(0.4))
            .force("charge", d3.forceManyBody().strength(-180))
            .force("center", d3.forceCenter(width/2, height/2))
            .force("collide", d3.forceCollide().radius(d=>{const nt=d.type||''; return (nt==='owner'||nt==='llevel')?25:(nt==='app'?20:15);}).strength(0.8))
            .on("end", () => { console.log("[ClusterViz] Simulation ended."); });

        window.clusterVizSimulation.on("tick", () => {
            if (!linkGroup.node() || !nodeGroup.node()) return;
            links.attr("x1", d=>d.source.x).attr("y1",d=>d.source.y).attr("x2",d=>d.target.x).attr("y2",d=>d.target.y);
            nodes.attr("transform", d=>`translate(${d.x},${d.y})`);
            labels
                .attr("x", d => d.x + 12)
                .attr("y", d => d.y + 4);
        });

        if (typeof createClusterLegend==='function') { console.log("[ClusterViz] Creating legend."); createClusterLegend(window.clusterVizSvg, colorScale, width, height); } else console.error("createClusterLegend function is not defined when called.");
        function dragStart(event, d) { if(!window.clusterVizSimulation)return; if(!event.active)window.clusterVizSimulation.alphaTarget(0.3).restart(); d.fx=d.x; d.fy=d.y; d3.select(this).raise(); if(window.clusterVizSvg)window.clusterVizSvg.style("cursor","grabbing"); }
        function dragging(event, d) { d.fx=event.x; d.fy=event.y; }
        function dragEnd(event, d) { if(!window.clusterVizSimulation)return; if(!event.active)window.clusterVizSimulation.alphaTarget(0); if(window.clusterVizSvg)window.clusterVizSvg.style("cursor","grab"); }
        console.log("[ClusterViz] Rendering complete.");
    })
    .catch(error => {
        console.error("[ClusterViz][ERROR] Cluster visualization failed:", error);
        const currentLoading = container.querySelector('.loading-spinner'); if (currentLoading) currentLoading.remove();
        window.clusterVizSvgGroup?.append("text").attr("x", width / 2).attr("y", height / 2).attr("text-anchor", "middle").attr("class", "error-message").text(`Erro ao carregar cluster: ${error.message}`);
        if (!window.clusterVizSvgGroup) showError(`Erro ao carregar cluster: ${error.message}`, 'clusterVisualizationContainer');
    });
}; // Fim da função window.loadClusterVisualization


/**
 * Creates a simple legend for the cluster visualization.
 */
function createClusterLegend(svg, colorScale, width, height) {
    svg.select(".cluster-legend").remove();
    const legendData = [ { type: 'product_patched', label: 'Product (Patched)', shape: d3.symbolCircle, size: 100, color: colorScale('product_patched') }, { type: 'product_unpatched', label: 'Product (Unpatched)', shape: d3.symbolCircle, size: 100, color: colorScale('product_unpatched') }, { type: 'app', label: 'Application', shape: d3.symbolSquare, size: 120, color: colorScale('app') }, { type: 'owner', label: 'Owner', shape: d3.symbolSquare, size: 150, color: colorScale('owner') }, { type: 'llevel', label: 'L-Level Group', shape: d3.symbolDiamond, size: 150, color: colorScale('llevel') } ];
    const legendGroup = svg.append("g").attr("class", "cluster-legend").attr("transform", `translate(15, 15)`);
    const legendItems = legendGroup.selectAll(".legend-item").data(legendData).join("g").attr("class", "legend-item").attr("transform", (d, i) => `translate(0, ${i * 22})`);
    legendItems.append("path").attr("d", d => d3.symbol().type(d.shape).size(d.size)()).attr("fill", d => d.color).attr("stroke", "#fff").attr("stroke-width", 1).attr("transform", "translate(10, 0)");
    legendItems.append("text").attr("x", 25).attr("y", 5).text(d => d.label).attr("font-size", "11px").attr("fill", "#333");
     setTimeout(() => { const bbox = legendGroup.node()?.getBBox(); if (bbox && legendGroup.node()) { legendGroup.insert("rect", ":first-child").attr("x", bbox.x-5).attr("y", bbox.y-5).attr("width", bbox.width+10).attr("height", bbox.height+10).attr("fill", "rgba(255, 255, 255, 0.85)").attr("stroke", "#ccc").attr("rx", 3); } }, 0);
}

/**
 * Creates pan and zoom controls for the cluster visualization.
 */
function createClusterControls(container) {
    if (container.querySelector('.cluster-controls')) return; // Avoid duplicates
    const controlsContainer = document.createElement('div'); controlsContainer.className = 'cluster-controls';
    Object.assign(controlsContainer.style, { position: 'absolute', top: '10px', right: '10px', zIndex: '100', display: 'flex', flexDirection: 'column', gap: '8px', backgroundColor: 'rgba(255, 255, 255, 0.7)', padding: '8px', borderRadius: '4px', boxShadow: '0 2px 5px rgba(0,0,0,0.2)' });
    const createButton = (iconClass, title, onClickAction) => { const button = document.createElement('button'); button.className = 'control-btn'; button.innerHTML = `<i class="${iconClass}"></i>`; button.title = title; button.onclick = onClickAction; Object.assign(button.style, { background: '#fff', border: '1px solid #ccc', borderRadius: '3px', padding: '5px 8px', cursor: 'pointer', display: 'block', marginBottom: '4px', fontSize: '12px', lineHeight: '1', minWidth: '30px', textAlign: 'center' }); button.onmouseover = () => button.style.backgroundColor = '#f0f0f0'; button.onmouseout = () => button.style.backgroundColor = '#fff'; return button; };
    const zoomControls = document.createElement('div'); zoomControls.className = 'control-group zoom-group';
    zoomControls.appendChild(createButton('fa-solid fa-plus', 'Zoom In', () => { if (window.clusterVizSvg && window.clusterVizZoom) window.clusterVizSvg.transition().duration(300).call(window.clusterVizZoom.scaleBy, 1.3); }));
    zoomControls.appendChild(createButton('fa-solid fa-minus', 'Zoom Out', () => { if (window.clusterVizSvg && window.clusterVizZoom) window.clusterVizSvg.transition().duration(300).call(window.clusterVizZoom.scaleBy, 1 / 1.3); }));
    controlsContainer.appendChild(zoomControls);
    const panControls = document.createElement('div'); panControls.className = 'control-group pan-group'; panControls.style.textAlign = 'center';
    const panTopRow = document.createElement('div'); panTopRow.appendChild(createButton('fa-solid fa-arrow-up', 'Pan Up', () => panClusterVisualization(0, 50))); panControls.appendChild(panTopRow);
    const panMiddleRow = document.createElement('div'); panMiddleRow.style.display = 'flex'; panMiddleRow.style.justifyContent = 'space-between'; panMiddleRow.appendChild(createButton('fa-solid fa-arrow-left', 'Pan Left', () => panClusterVisualization(50, 0))); panMiddleRow.appendChild(createButton('fa-solid fa-arrow-right', 'Pan Right', () => panClusterVisualization(-50, 0))); panControls.appendChild(panMiddleRow);
    const panBottomRow = document.createElement('div'); panBottomRow.appendChild(createButton('fa-solid fa-arrow-down', 'Pan Down', () => panClusterVisualization(0, -50))); panControls.appendChild(panBottomRow);
    controlsContainer.appendChild(panControls);
    const resetControl = document.createElement('div'); resetControl.className = 'control-group reset-group';
    resetControl.appendChild(createButton('fa-solid fa-expand', 'Reset View', resetClusterView)); // Changed icon
    controlsContainer.appendChild(resetControl);
    container.appendChild(controlsContainer);
    // console.log("Cluster controls created."); // Reduce noise
}

/**
 * Pans the cluster visualization by the specified delta x and y.
 */
function panClusterVisualization(dx, dy) {
    if (!window.clusterVizSvg || !window.clusterVizZoom) return;
    window.clusterVizSvg.transition().duration(300).call( window.clusterVizZoom.translateBy, dx / (window.clusterVizCurrentTransform?.k || 1), dy / (window.clusterVizCurrentTransform?.k || 1) );
}

/**
 * Resets the cluster visualization to its initial view.
 */
function resetClusterView() {
    if (!window.clusterVizSvg || !window.clusterVizZoom) return;
    window.clusterVizSvg.transition().duration(500).call( window.clusterVizZoom.transform, d3.zoomIdentity );
}

/**
 * Alterna o modo de tela cheia para o container de visualização do cluster.
 */
function toggleClusterFullscreen() {
    const container = document.getElementById('clusterVisualizationContainer');
    const button = document.getElementById('fullscreen-toggle-btn');
    if (!container || !button) { console.error("Container ou botão de tela cheia não encontrado."); return; }
    container.classList.toggle('fullscreen-viz');
    window.isClusterFullscreen = container.classList.contains('fullscreen-viz');
    console.log("Cluster fullscreen mode:", window.isClusterFullscreen);
    const icon = button.querySelector('i');
    if (icon) { if (window.isClusterFullscreen) { icon.classList.remove('fa-expand'); icon.classList.add('fa-compress'); button.title = 'Sair da Tela Cheia'; } else { icon.classList.remove('fa-compress'); icon.classList.add('fa-expand'); button.title = 'Alternar Tela Cheia'; } }
    window.dispatchEvent(new Event('resize')); // Trigger resize to potentially help SVG adjust
    // Recenter simulation (optional, might be slightly jarring)
    // if (window.clusterVizSimulation) {
    //      const currentWidth = container.clientWidth;
    //      const currentHeight = container.clientHeight;
    //      window.clusterVizSimulation.force("center", d3.forceCenter(currentWidth / 2, currentHeight / 2)).alpha(0.3).restart(); // Reheat simulation briefly
    // }
}


/**
 * Updates the content of the Organizational Recommendation Funnel tab.
 */
function updateOrganizationalRecommendations(orgRecommendations) {
    const orgTabPane = document.getElementById('org-funnel-tab'); if (!orgTabPane) { console.warn("Org Funnel tab pane not found."); return; }
    const funnelContainer = orgTabPane.querySelector('.funnel-container'); if (!funnelContainer) { console.error("Element with class 'funnel-container' not found within '#org-funnel-tab'."); orgTabPane.innerHTML = '<div class="error-message">Error displaying funnel: Structure missing.</div>'; return; }
    const getData = (levelKey, field, recommendations = orgRecommendations) => { const levelData = recommendations?.[levelKey]; if (!levelData || levelData[field] === undefined || levelData[field] === null) return (field === 'apps_affected') ? [] : 'N/A'; return levelData[field]; };
    const updateLevelText = (levelContainer, selector, text) => { const el = levelContainer?.querySelector(selector); if (el) el.textContent = text ?? 'N/A'; };
    const updateMetricCard = (levelContainer, cardIndex, labelText, valueText, detailText, successPercent = null) => { const card = levelContainer?.querySelectorAll('.metric-card')[cardIndex]; if (!card) return; updateLevelText(card, '.metric-label', labelText); updateLevelText(card, '.metric-value', valueText); updateLevelText(card, '.metric-detail', detailText); const progressBar = card.querySelector('.success-rate-progress'); const progressText = card.querySelector('.success-rate-text'); if (progressBar) { const percent = (typeof successPercent === 'number' && !isNaN(successPercent)) ? Math.max(0, Math.min(100, successPercent)) : 0; progressBar.style.width = `${percent}%`; progressBar.className = 'success-rate-progress'; if (percent >= 75) progressBar.classList.add('success-rate-high'); else if (percent >= 40) progressBar.classList.add('success-rate-medium'); else progressBar.classList.add('success-rate-low'); if (progressText) progressText.textContent = `${percent.toFixed(0)}%`; } else if (progressText) progressText.textContent = ''; };
    const updateAppTags = (levelContainer, apps) => { const tagsContainer = levelContainer?.querySelector('.app-tags'); if (!tagsContainer) return; tagsContainer.innerHTML = ''; if (Array.isArray(apps) && apps.length > 0) { const appsToShow = apps.slice(0, 5); appsToShow.forEach(app => { tagsContainer.innerHTML += `<div class="app-tag" title="${app}"><span>${app}</span></div>`; }); if (apps.length > 5) tagsContainer.innerHTML += `<div class="app-tag more-apps"><span>+${apps.length - 5} mais</span></div>`; } else tagsContainer.innerHTML = '<span class="no-data-message">N/A</span>'; };
    if (!orgRecommendations || Object.keys(orgRecommendations).length === 0) { funnelContainer.querySelectorAll('.funnel-level').forEach(level => { updateLevelText(level, '.level-title', level.dataset.levelName || 'Level'); const metricsContainer = level.querySelector('.funnel-metrics'); if (metricsContainer) metricsContainer.innerHTML = '<div class="no-data-message" style="grid-column: 1 / -1;">Nenhuma recomendação disponível.</div>'; updateLevelText(level, '.funnel-description', ''); updateAppTags(level, []); const tasksList = level.querySelector('.implementation-tasks ul'); if (tasksList) tasksList.innerHTML = ''; }); console.warn("Nenhum dado de recomendação organizacional disponível."); return; }
    const levelsToUpdate = [ { key: 'L4', selector: '.funnel-level.l4', name: 'Management' }, { key: 'L5', selector: '.funnel-level.l5', name: 'Team' }, { key: 'L6L7', selector: '.funnel-level.l6l7', name: 'Implementation' } ];
    levelsToUpdate.forEach(levelInfo => { const levelContainer = funnelContainer.querySelector(levelInfo.selector); if (!levelContainer) { console.error(`${levelInfo.key} level container (${levelInfo.selector}) not found.`); return; } const levelNameFromData = getData(levelInfo.key, 'level'); const displayTitle = levelNameFromData !== 'N/A' ? levelNameFromData : levelInfo.name; levelContainer.dataset.levelName = displayTitle; updateLevelText(levelContainer, '.level-title', displayTitle); const productData = getData(levelInfo.key, 'product'); const operationData = getData(levelInfo.key, 'operation'); const productName = Array.isArray(productData) ? productData[0] : 'N/A'; const productExecs = (Array.isArray(productData) && productData[1]) ? productData[1].executions || 0 : 0; const productSuccess = (Array.isArray(productData) && productData[1]) ? productData[1].success_rate : null; const operationName = Array.isArray(operationData) ? operationData[0] : 'N/A'; const operationSuccess = (Array.isArray(operationData) && operationData[1]) ? operationData[1].success_rate : null; updateMetricCard(levelContainer, 0, "Produto Principal:", productName, `(${productExecs} execuções)`, productSuccess); updateMetricCard(levelContainer, 1, "Operação Principal:", operationName, `(${(operationSuccess ?? 0).toFixed(0)}% sucesso)`, operationSuccess); updateLevelText(levelContainer, '.funnel-description', getData(levelInfo.key, 'description')); updateAppTags(levelContainer, getData(levelInfo.key, 'apps_affected')); const tasks = getData(levelInfo.key, 'implementation_tasks'); const tasksList = levelContainer.querySelector('.implementation-tasks ul'); if (tasksList) { tasksList.innerHTML = ''; if (Array.isArray(tasks) && tasks.length > 0) tasks.forEach(task => { const li = document.createElement('li'); li.textContent = task; tasksList.appendChild(li); }); } });
}


/**
 * Updates the content of the Product Rankings & Recommendations tab.
 */
function updateProductRecommendations(productRecommendations) {
    const productTabPane = document.getElementById('product-rankings-tab'); if (!productTabPane) { console.warn("Product rankings tab pane not found."); return; }
    const rankingsContainer = productTabPane.querySelector('.product-rankings'); const crossRecContainer = productTabPane.querySelector('.product-recommendations');
    if (!rankingsContainer) { console.error("Element '.product-rankings' not found within '#product-rankings-tab'."); productTabPane.innerHTML = '<div class="error-message">Error displaying rankings: Container missing.</div>'; return; }
    rankingsContainer.innerHTML = ''; if (crossRecContainer) crossRecContainer.innerHTML = '';
    const topProducts = productRecommendations?.top_products;
    if (Array.isArray(topProducts) && topProducts.length > 0) { rankingsContainer.innerHTML += '<h3>Produtos Principais por Uso</h3>'; topProducts.slice(0, 5).forEach((productInfo, index) => { if (!Array.isArray(productInfo) || productInfo.length < 2 || typeof productInfo[1] !== 'object') { console.warn("Skipping invalid product ranking item:", productInfo); return; } const [product, stats] = productInfo; const successRate = Number(stats?.success_rate) || 0; const executions = Number(stats?.executions) || 0; rankingsContainer.innerHTML += `<div class="product-rank"><div class="rank">${index + 1}</div><div class="product-details"><div class="product-name">${product || 'N/A'}</div><div class="product-stats">${executions} execuções - ${successRate.toFixed(0)}% sucesso</div></div></div>`; }); } else { rankingsContainer.innerHTML = `<div class="no-data-message">Nenhum dado de ranking de produto disponível.</div>`; }
    if (crossRecContainer) { const crossRecs = productRecommendations?.cross_product_recommendations; if (Array.isArray(crossRecs) && crossRecs.length > 0) { crossRecContainer.innerHTML += '<h3>Recomendações Cross-Product</h3>'; crossRecs.forEach(rec => { const productsText = Array.isArray(rec.products) ? rec.products.join(' + ') : 'N/A'; const combinedSuccess = Number(rec.combined_success) || 0; crossRecContainer.innerHTML += `<div class="recommendation-item"><div class="recommendation-title">${productsText}</div><div class="recommendation-stats">${combinedSuccess.toFixed(0)}% sucesso combinado</div><div class="recommendation-text">${rec.description || 'Considere usar estes produtos juntos.'}</div></div>`; }); } else { crossRecContainer.innerHTML = `<div class="no-data-message">Nenhuma recomendação cross-product disponível.</div>`; } }
}


// --- Horizontal L-Level Filtering (Analysis) ---
function filterProductsBySameLLevel() {
    if (!allApplications || allApplications.length === 0) return;
    const productsByLevel = {};
    allApplications.forEach(app => { const level = app.lLevel; if (!level || level === 'N/A' || level === 'Unknown' || !/^L\d$/.test(level)) return; if (!productsByLevel[level]) productsByLevel[level] = {}; const product = app.mostCommonProduct; if (product && product !== 'N/A') productsByLevel[level][product] = (productsByLevel[level][product] || 0) + 1; });
    // Object.entries(productsByLevel).forEach(([level, products]) => { const commonProducts = Object.entries(products).filter(([, count]) => count >= 2).sort(([, countA], [, countB]) => countB - countA).map(([productName, count]) => `${productName} (${count} apps)`).join(', '); if (commonProducts) console.log(`Level ${level} Common Products: ${commonProducts}`); });
}

// --- Org Funnel Interactivity ---
function setupFunnelInteractivity() {
    const orgTabPane = document.getElementById('org-funnel-tab'); if (!orgTabPane) return;
    const funnelLevels = orgTabPane.querySelectorAll('.funnel-level'); if(!funnelLevels || funnelLevels.length === 0) return;
    funnelLevels.forEach(level => { level.addEventListener('click', function() { this.classList.toggle('active'); }); level.addEventListener('mouseenter', function() { this.classList.add('hovered'); }); level.addEventListener('mouseleave', function() { this.classList.remove('hovered'); }); });
    const arrows = orgTabPane.querySelectorAll('.funnel-arrow'); arrows.forEach(arrow => { arrow.addEventListener('mouseenter', function() { this.classList.add('hovered'); }); arrow.addEventListener('mouseleave', function() { this.classList.remove('hovered'); }); });
}

// --- Add CSS for Controls (Optional but Recommended) ---
const controlStyles = `
    /* Estilos para os botões de controle do cluster */
    .cluster-controls .control-btn {
        background-color: rgba(0, 118, 206, 0.8); /* Fundo do botão transparente */
        border: none; /* Remove borda padrão */
        border-radius: 3px;
        padding: 6px 8px; /* Ajuste o padding se necessário */
        margin-bottom: 3px; /* Espaçamento entre botões */
        cursor: pointer;
        transition: background-color 0.2s ease;
        font-size: 14px; /* Tamanho do ícone */
        line-height: 1;
        min-width: 32px; /* Largura mínima */
        text-align: center;
        display: block; /* Garante que ocupem a linha */
    }
    .cluster-controls .control-btn i {
        color: white; /* Cor do ÍCONE = branco */
        vertical-align: middle;
    }
    .cluster-controls .control-btn:hover {
        background-color: rgba(255,255,255,0.2); /* Efeito hover leve */
    }
    /* Estilo do container dos controles */
    .cluster-controls {
        background-color: rgba(0, 118, 206, 0.8); /* Fundo AZUL semi-transparente */
        box-shadow: 0 1px 4px rgba(0,0,0,0.3);
        border-radius: 4px;
        padding: 8px;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1001; /* Garante que fique sobre a visualização */
        display: flex;
        flex-direction: column;
        gap: 5px; /* Espaço entre grupos de botões */
    }
    .cluster-controls .control-group {
        margin-bottom: 5px; /* Espaço abaixo de cada grupo */
    }
     .cluster-controls .pan-group {
         text-align: center;
     }
    .cluster-controls .pan-group .control-btn {
        margin-left: 1px; margin-right: 1px; /* Pequeno espaço entre botões de pan laterais */
    }
    /* Estilos básicos para mensagens dentro do SVG */
    .cluster-svg .no-data-message, .cluster-svg .error-message { font-size: 14px; fill: #666; }
    .cluster-legend { font-family: sans-serif; }

    /* --- Estilos Fullscreen (Mantidos como antes) --- */
    #clusterVisualizationContainer.fullscreen-viz { position: fixed; top: 10px; left: 10px; right: 10px; bottom: 10px; width: calc(100vw - 20px); height: calc(100vh - 20px); background-color: #f9f9f9; z-index: 1000; border: 1px solid #aaa; box-shadow: 0 0 15px rgba(0,0,0,0.3); transition: all 0.3s ease-in-out; }
    #clusterVisualizationContainer.fullscreen-viz .cluster-svg { width: 100%; height: 100%; }
    #clusterVisualizationContainer .cluster-controls { position: absolute; top: 10px; right: 10px; z-index: 1001; background-color: rgba(0, 118, 206, 0.8); /* Mantém fundo azul no modo fullscreen */ }
    #fullscreen-toggle-btn { position: absolute; bottom: 15px; right: 15px; z-index: 1001; background-color: rgba(255, 255, 255, 0.8); border: 1px solid #ccc; border-radius: 4px; padding: 5px 8px; cursor: pointer; font-size: 14px; line-height: 1; box-shadow: 0 1px 3px rgba(0,0,0,0.2); }
    #fullscreen-toggle-btn:hover { background-color: #f0f0f0; }
    #fullscreen-toggle-btn i { color: #333; } /* Cor do ícone do botão fullscreen (normal) */
    #clusterVisualizationContainer.fullscreen-viz #fullscreen-toggle-btn { background-color: rgba(0, 118, 206, 0.8); border: 1px solid #fff; } /* Estilo diferente no modo fullscreen */
    #clusterVisualizationContainer.fullscreen-viz #fullscreen-toggle-btn i { color: white; } /* Ícone branco no modo fullscreen */
`;

if (!document.getElementById('dashboard-control-styles')) {
     const styleSheet = document.createElement("style");
     styleSheet.id = 'dashboard-control-styles';
     styleSheet.type = "text/css";
     styleSheet.innerText = controlStyles;
     document.head.appendChild(styleSheet);
}
